/**
 * Simple PDF Extractor Test
 */

console.log('🧪 Testing PDF Extractor...');

try {
  // Test the PDF extractor directly
  const { PDFExtractor } = await import('../LearnMCP/modules/content-extractors/pdf-extractor.js');
  
  console.log('✅ PDF Extractor imported successfully');
  
  const extractor = new PDFExtractor();
  console.log('✅ PDF Extractor instantiated');
  
  // Create a simple test file
  const fs = await import('fs/promises');
  const path = await import('path');
  
  const testContent = `
# Trading Curriculum - ICT Methodology

## Module 1: Market Structure
- Understanding market phases
- Identifying trend changes
- Support and resistance levels

## Module 2: Price Action Analysis  
- Candlestick patterns
- Chart patterns
- Volume analysis
  `;
  
  const testFile = 'test-curriculum.txt';
  await fs.writeFile(testFile, testContent);
  console.log('✅ Test file created');
  
  // Test extraction
  const result = await extractor.extract(testFile);
  console.log('✅ Extraction completed');
  
  console.log('📊 Results:');
  console.log(`- Type: ${result.type}`);
  console.log(`- Title: ${result.metadata.title}`);
  console.log(`- Modules: ${result.content.curriculum.modules.length}`);
  console.log(`- Difficulty: ${result.content.curriculum.difficulty}`);
  
  // Cleanup
  await fs.unlink(testFile);
  console.log('✅ Test completed successfully!');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
}
