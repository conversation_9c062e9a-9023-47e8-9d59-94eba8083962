/**
 * Curriculum Integration Test
 * Tests the complete PDF curriculum integration workflow
 */

import { jest } from '@jest/globals';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Mock data for testing
const mockCurriculumData = {
  type: 'pdf',
  url: 'test-curriculum.pdf',
  metadata: {
    title: 'ICT Trading Curriculum',
    fileSize: 1024,
    extractedAt: new Date().toISOString(),
    modules: 5
  },
  content: {
    text: 'Trading curriculum content...',
    wordCount: 500,
    curriculum: {
      title: 'ICT Trading Methodology',
      modules: [
        {
          title: 'Module 1: Market Structure',
          topics: ['Market phases', 'Trend changes', 'Support/Resistance'],
          difficulty: 'intermediate'
        },
        {
          title: 'Module 2: Price Action',
          topics: ['Candlesticks', 'Chart patterns', 'Volume'],
          difficulty: 'intermediate'
        },
        {
          title: 'Module 3: Risk Management',
          topics: ['Position sizing', 'Stop loss', 'Risk-reward'],
          difficulty: 'advanced'
        },
        {
          title: 'Module 4: Psychology',
          topics: ['Emotional control', 'Discipline', 'Performance'],
          difficulty: 'advanced'
        },
        {
          title: 'Module 5: Advanced Strategies',
          topics: ['Scalping', 'Swing trading', 'Long-term'],
          difficulty: 'expert'
        }
      ],
      totalModules: 5,
      difficulty: 'intermediate',
      estimatedDuration: '12 weeks'
    }
  }
};

describe('Curriculum Integration Workflow', () => {
  let dataPersistence;
  let memorySync;
  let curriculumIntegration;
  let htaTreeBuilder;
  let testProjectId;
  let testDataDir;

  beforeAll(async () => {
    // Setup test environment
    testProjectId = `test_curriculum_${Date.now()}`;
    testDataDir = join(__dirname, '..', '.test-curriculum-data');
    
    try {
      await fs.mkdir(testDataDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }
  });

  beforeEach(async () => {
    // Import modules
    const { DataPersistence } = await import('../modules/data-persistence.js');
    const { MemorySync } = await import('../modules/memory-sync.js');
    const { CurriculumIntegration } = await import('../modules/curriculum-integration.js');
    const { HtaTreeBuilder } = await import('../modules/hta-tree-builder.js');

    // Initialize components
    dataPersistence = new DataPersistence(testDataDir);
    memorySync = new MemorySync(dataPersistence);
    curriculumIntegration = new CurriculumIntegration(dataPersistence, memorySync);
    htaTreeBuilder = new HtaTreeBuilder(dataPersistence, null, memorySync);
  });

  afterAll(async () => {
    // Cleanup test data
    try {
      await fs.rm(testDataDir, { recursive: true, force: true });
    } catch (error) {
      console.warn('Cleanup warning:', error.message);
    }
  });

  test('should import curriculum integration module successfully', async () => {
    expect(curriculumIntegration).toBeDefined();
    expect(typeof curriculumIntegration.storeCurriculumData).toBe('function');
    expect(typeof curriculumIntegration.getCurriculumForComplexityAnalysis).toBe('function');
  });

  test('should store curriculum data successfully', async () => {
    const result = await curriculumIntegration.storeCurriculumData(testProjectId, mockCurriculumData);
    
    expect(result.success).toBe(true);
    expect(result.modules).toBe(5);
    expect(result.complexity).toBeDefined();
    expect(result.complexity.score).toBeGreaterThan(0);
  });

  test('should retrieve curriculum data for complexity analysis', async () => {
    // First store the data
    await curriculumIntegration.storeCurriculumData(testProjectId, mockCurriculumData);
    
    // Then retrieve it
    const retrievedData = await curriculumIntegration.getCurriculumForComplexityAnalysis(testProjectId);
    
    expect(retrievedData).toBeDefined();
    expect(retrievedData.hasCurriculum).toBe(true);
    expect(retrievedData.totalModules).toBe(5);
    expect(retrievedData.difficulty).toBe('intermediate');
    expect(retrievedData.complexityFactors).toBeDefined();
  });

  test('should enhance complexity analysis with curriculum data', async () => {
    // Store curriculum data
    await curriculumIntegration.storeCurriculumData(testProjectId, mockCurriculumData);
    const curriculumData = await curriculumIntegration.getCurriculumForComplexityAnalysis(testProjectId);
    
    // Test complexity analysis without curriculum
    const baseComplexity = htaTreeBuilder.analyzeGoalComplexity(
      'Master ICT trading methodology',
      'Learn professional trading'
    );
    
    // Test complexity analysis with curriculum
    const enhancedComplexity = htaTreeBuilder.analyzeGoalComplexity(
      'Master ICT trading methodology',
      'Learn professional trading',
      curriculumData
    );
    
    expect(enhancedComplexity.curriculumEnhanced).toBe(true);
    expect(enhancedComplexity.curriculumModules).toBe(5);
    expect(enhancedComplexity.score).toBeGreaterThanOrEqual(baseComplexity.score);
  });

  test('should calculate curriculum-informed tree structure', async () => {
    // Store curriculum data
    await curriculumIntegration.storeCurriculumData(testProjectId, mockCurriculumData);
    const curriculumData = await curriculumIntegration.getCurriculumForComplexityAnalysis(testProjectId);
    
    // Test tree structure calculation
    const complexityScore = 6;
    const treeStructure = htaTreeBuilder.calculateTreeStructure(complexityScore, curriculumData);
    
    expect(treeStructure).toBeDefined();
    expect(treeStructure.mainBranches).toBe(5); // Should match curriculum modules
    expect(treeStructure.depth).toBeGreaterThanOrEqual(3); // Should be deep enough for curriculum
  });

  test('should store curriculum context in memory sync', async () => {
    // Store curriculum data
    await curriculumIntegration.storeCurriculumData(testProjectId, mockCurriculumData);
    
    // Verify memory sync has curriculum context
    const curriculumContext = await memorySync.getCurriculumContext(testProjectId);
    
    expect(curriculumContext).toBeDefined();
    expect(curriculumContext.totalModules).toBe(5);
    expect(curriculumContext.difficulty).toBe('intermediate');
  });
});
