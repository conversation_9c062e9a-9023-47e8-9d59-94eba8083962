/**
 * Curriculum Integration Module
 * Handles integration of external curriculum content (PDFs, etc.) into Forest workflow
 */

export class CurriculumIntegration {
  constructor(dataPersistence, memorySync) {
    this.dataPersistence = dataPersistence;
    this.memorySync = memorySync;
  }

  /**
   * Store curriculum data from LearnMCP extraction
   */
  async storeCurriculumData(projectId, curriculumData) {
    try {
      // Store curriculum in project data
      await this.dataPersistence.saveProjectData(projectId, 'curriculum.json', {
        ...curriculumData,
        storedAt: new Date().toISOString(),
        source: 'learnmcp_extraction'
      });

      // Store curriculum context in memory for Forest access
      await this.memorySync.storeCurriculumContext(projectId, {
        title: curriculumData.metadata?.title || 'Unknown Curriculum',
        modules: curriculumData.content?.curriculum?.modules || [],
        difficulty: curriculumData.content?.curriculum?.difficulty || 'intermediate',
        totalModules: curriculumData.content?.curriculum?.totalModules || 0,
        estimatedDuration: curriculumData.content?.curriculum?.estimatedDuration || 'Unknown',
        structure: curriculumData.content?.structure || {},
        extractedAt: curriculumData.metadata?.extractedAt
      });

      return {
        success: true,
        message: 'Curriculum data stored successfully',
        modules: curriculumData.content?.curriculum?.totalModules || 0
      };
    } catch (error) {
      throw new Error(`Failed to store curriculum data: ${error.message}`);
    }
  }

  /**
   * Retrieve curriculum data for complexity analysis
   */
  async getCurriculumForComplexityAnalysis(projectId) {
    try {
      const curriculumData = await this.dataPersistence.loadProjectData(projectId, 'curriculum.json');
      
      if (!curriculumData) {
        return null;
      }

      // Transform curriculum data for complexity analysis
      return {
        hasCurriculum: true,
        modules: curriculumData.content?.curriculum?.modules || [],
        totalModules: curriculumData.content?.curriculum?.totalModules || 0,
        difficulty: curriculumData.content?.curriculum?.difficulty || 'intermediate',
        estimatedDuration: curriculumData.content?.curriculum?.estimatedDuration,
        structure: curriculumData.content?.structure || {},
        prerequisites: curriculumData.content?.curriculum?.prerequisites || [],
        complexityFactors: this.analyzeCurriculumComplexity(curriculumData)
      };
    } catch (error) {
      console.warn('[CurriculumIntegration] Failed to load curriculum data:', error.message);
      return null;
    }
  }

  /**
   * Analyze curriculum complexity for HTA tree building
   */
  analyzeCurriculumComplexity(curriculumData) {
    const curriculum = curriculumData.content?.curriculum;
    const structure = curriculumData.content?.structure;
    
    if (!curriculum) {
      return { score: 5, factors: [] };
    }

    const factors = [];
    let complexityScore = 3; // Base score

    // Module count complexity
    const moduleCount = curriculum.totalModules || 0;
    if (moduleCount > 8) {
      complexityScore += 2;
      factors.push('High module count (8+)');
    } else if (moduleCount > 5) {
      complexityScore += 1;
      factors.push('Moderate module count (5-8)');
    }

    // Difficulty level
    if (curriculum.difficulty === 'advanced') {
      complexityScore += 2;
      factors.push('Advanced difficulty level');
    } else if (curriculum.difficulty === 'intermediate') {
      complexityScore += 1;
      factors.push('Intermediate difficulty level');
    }

    // Topic depth analysis
    const totalTopics = curriculum.modules?.reduce((sum, module) => 
      sum + (module.topics?.length || 0), 0) || 0;
    
    if (totalTopics > 30) {
      complexityScore += 2;
      factors.push('High topic density (30+ topics)');
    } else if (totalTopics > 15) {
      complexityScore += 1;
      factors.push('Moderate topic density (15-30 topics)');
    }

    // Prerequisites complexity
    const prereqCount = curriculum.prerequisites?.length || 0;
    if (prereqCount > 3) {
      complexityScore += 1;
      factors.push('Multiple prerequisites required');
    }

    // Structure complexity
    const headingLevels = structure?.headings?.map(h => h.level) || [];
    const maxDepth = Math.max(...headingLevels, 0);
    if (maxDepth > 3) {
      complexityScore += 1;
      factors.push('Deep hierarchical structure');
    }

    return {
      score: Math.min(complexityScore, 10), // Cap at 10
      factors,
      moduleCount,
      totalTopics,
      maxDepth,
      difficulty: curriculum.difficulty
    };
  }

  /**
   * Generate curriculum-informed HTA structure suggestions
   */
  generateCurriculumHTAStructure(curriculumData, userGoal, userContext) {
    const curriculum = curriculumData.content?.curriculum;
    
    if (!curriculum || !curriculum.modules) {
      return null;
    }

    // Map curriculum modules to HTA branches
    const htaBranches = curriculum.modules.map((module, index) => ({
      id: `curriculum_branch_${index + 1}`,
      title: module.title,
      description: `Learn ${module.title.toLowerCase()} concepts and applications`,
      difficulty: module.difficulty || 'intermediate',
      order: module.order || index + 1,
      topics: module.topics || [],
      estimatedTasks: Math.max(3, Math.min(module.topics?.length || 5, 12)),
      source: 'curriculum_module'
    }));

    // Calculate recommended tree structure
    const totalBranches = htaBranches.length;
    const avgTopicsPerBranch = curriculum.modules.reduce((sum, m) => 
      sum + (m.topics?.length || 0), 0) / totalBranches;

    return {
      recommendedStructure: {
        mainBranches: totalBranches,
        subBranchesPerMain: Math.ceil(avgTopicsPerBranch / 3),
        tasksPerLeaf: 4,
        totalDepth: totalBranches > 6 ? 4 : 3
      },
      curriculumBranches: htaBranches,
      integrationStrategy: {
        alignWithGoal: this.alignCurriculumWithGoal(curriculum, userGoal),
        contextualAdaptations: this.suggestContextualAdaptations(curriculum, userContext),
        progressionPath: this.generateProgressionPath(curriculum.modules)
      }
    };
  }

  /**
   * Align curriculum with user goal
   */
  alignCurriculumWithGoal(curriculum, userGoal) {
    if (!userGoal) return [];

    const goalKeywords = userGoal.toLowerCase().split(/\s+/);
    const alignments = [];

    curriculum.modules?.forEach(module => {
      const moduleText = (module.title + ' ' + 
        (module.topics?.map(t => t.title).join(' ') || '')).toLowerCase();
      
      const relevantKeywords = goalKeywords.filter(keyword => 
        moduleText.includes(keyword));
      
      if (relevantKeywords.length > 0) {
        alignments.push({
          module: module.title,
          relevance: relevantKeywords.length / goalKeywords.length,
          keywords: relevantKeywords,
          priority: relevantKeywords.length > 1 ? 'high' : 'medium'
        });
      }
    });

    return alignments.sort((a, b) => b.relevance - a.relevance);
  }

  /**
   * Suggest contextual adaptations
   */
  suggestContextualAdaptations(curriculum, userContext) {
    const adaptations = [];

    if (userContext?.includes('beginner') || userContext?.includes('new')) {
      adaptations.push({
        type: 'pace_adjustment',
        suggestion: 'Add foundational tasks before each module',
        reason: 'Beginner context detected'
      });
    }

    if (userContext?.includes('time') || userContext?.includes('busy')) {
      adaptations.push({
        type: 'time_optimization',
        suggestion: 'Break modules into smaller, focused sessions',
        reason: 'Time constraints mentioned'
      });
    }

    if (userContext?.includes('practical') || userContext?.includes('hands-on')) {
      adaptations.push({
        type: 'practical_focus',
        suggestion: 'Emphasize practical exercises and real-world applications',
        reason: 'Practical learning preference detected'
      });
    }

    return adaptations;
  }

  /**
   * Generate progression path from curriculum modules
   */
  generateProgressionPath(modules) {
    if (!modules || modules.length === 0) return [];

    return modules.map((module, index) => ({
      stage: index + 1,
      title: module.title,
      prerequisites: index === 0 ? [] : [modules[index - 1].title],
      outcomes: module.topics?.map(t => `Understand ${t.title.toLowerCase()}`) || [],
      nextSteps: index < modules.length - 1 ? [modules[index + 1].title] : ['Apply knowledge in practice']
    }));
  }

  /**
   * Check if project has curriculum data
   */
  async hasCurriculumData(projectId) {
    try {
      const curriculumData = await this.dataPersistence.loadProjectData(projectId, 'curriculum.json');
      return !!curriculumData;
    } catch {
      return false;
    }
  }
}
