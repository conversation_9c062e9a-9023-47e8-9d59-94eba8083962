/**
 * Simple test to verify curriculum integration works
 */

import { DataPersistence } from './modules/data-persistence.js';
import { MemorySync } from './modules/memory-sync.js';
import { CurriculumIntegration } from './modules/curriculum-integration.js';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testCurriculumIntegration() {
  console.log('🧪 Testing Curriculum Integration...');
  
  try {
    // Setup test environment
    const testDataDir = join(__dirname, '.test-curriculum-data');
    const testProjectId = `test_curriculum_${Date.now()}`;
    
    // Initialize components
    const dataPersistence = new DataPersistence(testDataDir);
    const memorySync = new MemorySync(dataPersistence);
    const curriculumIntegration = new CurriculumIntegration(dataPersistence, memorySync);
    
    console.log('✅ Components initialized successfully');
    
    // Mock curriculum data
    const mockCurriculumData = {
      type: 'pdf',
      url: 'test-curriculum.pdf',
      metadata: {
        title: 'Test Trading Curriculum',
        fileSize: 1024,
        extractedAt: new Date().toISOString(),
        modules: 3
      },
      content: {
        text: 'Test curriculum content...',
        wordCount: 300,
        curriculum: {
          title: 'Test Trading Methodology',
          modules: [
            {
              title: 'Module 1: Basics',
              topics: ['Introduction', 'Fundamentals'],
              difficulty: 'beginner'
            },
            {
              title: 'Module 2: Intermediate',
              topics: ['Advanced concepts', 'Practice'],
              difficulty: 'intermediate'
            },
            {
              title: 'Module 3: Advanced',
              topics: ['Expert strategies', 'Real trading'],
              difficulty: 'advanced'
            }
          ],
          totalModules: 3,
          difficulty: 'intermediate',
          estimatedDuration: '8 weeks'
        }
      }
    };
    
    // Test storing curriculum data
    console.log('📚 Testing curriculum data storage...');
    const storeResult = await curriculumIntegration.storeCurriculumData(testProjectId, mockCurriculumData);
    
    if (storeResult.success) {
      console.log('✅ Curriculum data stored successfully');
      console.log(`   - Modules: ${storeResult.modules}`);
      console.log(`   - Complexity Score: ${storeResult.complexity.score}`);
      console.log(`   - Complexity Factors: ${storeResult.complexity.factors.join(', ')}`);
    } else {
      console.error('❌ Failed to store curriculum data:', storeResult.error);
      return;
    }
    
    // Test retrieving curriculum data
    console.log('📖 Testing curriculum data retrieval...');
    const retrievedData = await curriculumIntegration.getCurriculumForComplexityAnalysis(testProjectId);
    
    if (retrievedData.hasCurriculum) {
      console.log('✅ Curriculum data retrieved successfully');
      console.log(`   - Has Curriculum: ${retrievedData.hasCurriculum}`);
      console.log(`   - Total Modules: ${retrievedData.totalModules}`);
      console.log(`   - Difficulty: ${retrievedData.difficulty}`);
      console.log(`   - Complexity Score: ${retrievedData.complexityScore}`);
    } else {
      console.error('❌ Failed to retrieve curriculum data');
      return;
    }
    
    // Test memory sync integration
    console.log('🧠 Testing memory sync integration...');
    const curriculumContext = await memorySync.getCurriculumContext(testProjectId);
    
    if (curriculumContext) {
      console.log('✅ Curriculum context found in memory sync');
      console.log(`   - Total Modules: ${curriculumContext.totalModules}`);
      console.log(`   - Difficulty: ${curriculumContext.difficulty}`);
      console.log(`   - Complexity Score: ${curriculumContext.complexityScore}`);
    } else {
      console.error('❌ Curriculum context not found in memory sync');
      return;
    }
    
    console.log('\n🎉 All curriculum integration tests passed!');
    console.log('\n📋 Integration Summary:');
    console.log('   ✅ CurriculumIntegration module imports correctly');
    console.log('   ✅ Can store curriculum data from LearnMCP format');
    console.log('   ✅ Analyzes curriculum complexity for HTA tree building');
    console.log('   ✅ Integrates with MemorySync for context storage');
    console.log('   ✅ Provides data in format expected by HTA tree builder');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testCurriculumIntegration();
