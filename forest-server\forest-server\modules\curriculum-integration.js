/**
 * Curriculum Integration Module
 * Handles integration of external curriculum content (PDFs, etc.) into Forest workflow
 */

export class CurriculumIntegration {
  constructor(dataPersistence, memorySync) {
    this.dataPersistence = dataPersistence;
    this.memorySync = memorySync;
  }

  /**
   * Store curriculum data from LearnMCP extraction
   */
  async storeCurriculumData(projectId, curriculumData) {
    try {
      // Store curriculum in project data
      await this.dataPersistence.saveProjectData(projectId, 'curriculum.json', {
        ...curriculumData,
        storedAt: new Date().toISOString(),
        processedForForest: true
      });

      // Analyze curriculum complexity
      const complexity = this.analyzeCurriculumComplexity(curriculumData);
      
      // Store complexity analysis
      await this.dataPersistence.saveProjectData(projectId, 'curriculum-complexity.json', complexity);

      // Update memory sync with curriculum context
      await this.memorySync.storeCurriculumContext(projectId, {
        hasCurriculum: true,
        totalModules: curriculumData.content?.curriculum?.totalModules || 0,
        difficulty: curriculumData.content?.curriculum?.difficulty || 'unknown',
        estimatedDuration: curriculumData.content?.curriculum?.estimatedDuration,
        complexityScore: complexity.score
      });

      return {
        success: true,
        modules: curriculumData.content?.curriculum?.totalModules || 0,
        complexity: complexity
      };
    } catch (error) {
      console.error('Error storing curriculum data:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get curriculum data formatted for complexity analysis
   */
  async getCurriculumForComplexityAnalysis(projectId) {
    try {
      const curriculumData = await this.dataPersistence.loadProjectData(projectId, 'curriculum.json');
      const complexityData = await this.dataPersistence.loadProjectData(projectId, 'curriculum-complexity.json');
      
      if (!curriculumData) {
        return {
          hasCurriculum: false,
          totalModules: 0,
          difficulty: 'unknown',
          complexityFactors: []
        };
      }

      const curriculum = curriculumData.content?.curriculum;
      
      return {
        hasCurriculum: true,
        totalModules: curriculum?.totalModules || 0,
        difficulty: curriculum?.difficulty || 'unknown',
        estimatedDuration: curriculum?.estimatedDuration,
        modules: curriculum?.modules || [],
        complexityFactors: complexityData?.factors || [],
        complexityScore: complexityData?.score || 0
      };
    } catch (error) {
      console.error('Error retrieving curriculum for complexity analysis:', error);
      return {
        hasCurriculum: false,
        totalModules: 0,
        difficulty: 'unknown',
        complexityFactors: []
      };
    }
  }

  /**
   * Analyze curriculum complexity for HTA tree building
   */
  analyzeCurriculumComplexity(curriculumData) {
    const curriculum = curriculumData.content?.curriculum;
    if (!curriculum) {
      return { score: 0, factors: [] };
    }

    const factors = [];
    let score = 0;

    // Module count factor
    const moduleCount = curriculum.totalModules || 0;
    if (moduleCount > 0) {
      const moduleScore = Math.min(moduleCount * 0.5, 3);
      score += moduleScore;
      factors.push(`${moduleCount} modules (+${moduleScore})`);
    }

    // Difficulty factor
    const difficultyMap = {
      'beginner': 1,
      'intermediate': 2,
      'advanced': 3,
      'expert': 4
    };
    const difficultyScore = difficultyMap[curriculum.difficulty] || 1;
    score += difficultyScore;
    factors.push(`${curriculum.difficulty} difficulty (+${difficultyScore})`);

    // Duration factor
    if (curriculum.estimatedDuration) {
      const durationMatch = curriculum.estimatedDuration.match(/(\d+)/);
      if (durationMatch) {
        const weeks = parseInt(durationMatch[1]);
        const durationScore = Math.min(weeks * 0.1, 2);
        score += durationScore;
        factors.push(`${weeks} weeks duration (+${durationScore})`);
      }
    }

    // Module complexity factor
    if (curriculum.modules && Array.isArray(curriculum.modules)) {
      const complexModules = curriculum.modules.filter(m => 
        m.difficulty === 'advanced' || m.difficulty === 'expert'
      ).length;
      if (complexModules > 0) {
        const complexScore = complexModules * 0.3;
        score += complexScore;
        factors.push(`${complexModules} complex modules (+${complexScore})`);
      }
    }

    return {
      score: Math.round(score * 10) / 10,
      factors: factors,
      moduleCount: moduleCount,
      difficulty: curriculum.difficulty,
      estimatedWeeks: curriculum.estimatedDuration
    };
  }

  /**
   * Check if project has curriculum data
   */
  async hasCurriculumData(projectId) {
    try {
      const curriculumData = await this.dataPersistence.loadProjectData(projectId, 'curriculum.json');
      return !!curriculumData;
    } catch {
      return false;
    }
  }
}
