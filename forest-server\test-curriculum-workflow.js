/**
 * Test Curriculum Integration Workflow
 * Tests the complete PDF → LearnMCP → MemoryMCP → Forest → HTA Tree workflow
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test configuration
const TEST_CONFIG = {
  projectId: 'test_curriculum_' + Date.now(),
  dataDir: join(__dirname, '.test-data'),
  testPdfPath: join(__dirname, 'test-curriculum.pdf'),
  goal: 'Master ICT trading methodology for consistent profitability',
  context: 'I want to learn professional trading using the Inner Circle Trader methodology, focusing on practical application and risk management'
};

class CurriculumWorkflowTest {
  constructor() {
    this.results = {
      steps: [],
      success: false,
      errors: [],
      timing: {}
    };
  }

  async runCompleteWorkflow() {
    console.log('🚀 Starting Curriculum Integration Workflow Test\n');
    
    try {
      // Step 1: Setup test environment
      await this.setupTestEnvironment();
      
      // Step 2: Test LearnMCP PDF extraction
      await this.testLearnMCPExtraction();
      
      // Step 3: Test curriculum storage in MemoryMCP
      await this.testMemoryMCPStorage();
      
      // Step 4: Test Forest curriculum integration
      await this.testForestIntegration();
      
      // Step 5: Test HTA tree building with curriculum
      await this.testHTATreeBuilding();
      
      // Step 6: Verify end-to-end workflow
      await this.verifyEndToEndWorkflow();
      
      this.results.success = true;
      console.log('✅ All tests passed! Curriculum workflow is working correctly.\n');
      
    } catch (error) {
      this.results.errors.push(error.message);
      console.error('❌ Test failed:', error.message);
    } finally {
      await this.cleanup();
      this.printResults();
    }
  }

  async setupTestEnvironment() {
    const startTime = Date.now();
    console.log('📁 Setting up test environment...');
    
    try {
      // Create test data directory
      await fs.mkdir(TEST_CONFIG.dataDir, { recursive: true });
      
      // Create mock PDF content for testing
      await this.createMockPDF();
      
      this.results.steps.push('✅ Test environment setup');
      this.results.timing.setup = Date.now() - startTime;
      console.log('   ✓ Test environment ready\n');
      
    } catch (error) {
      throw new Error(`Setup failed: ${error.message}`);
    }
  }

  async createMockPDF() {
    // Create a simple text file to simulate PDF content
    const mockPdfContent = `
# Trading Curriculum - ICT Methodology

## Module 1: Market Structure
- Understanding market phases
- Identifying trend changes
- Support and resistance levels

## Module 2: Price Action Analysis  
- Candlestick patterns
- Chart patterns
- Volume analysis

## Module 3: Risk Management
- Position sizing
- Stop loss strategies
- Risk-reward ratios

## Module 4: Trading Psychology
- Emotional control
- Discipline development
- Performance tracking

## Module 5: Advanced Strategies
- Scalping techniques
- Swing trading
- Long-term investing
    `;
    
    await fs.writeFile(TEST_CONFIG.testPdfPath, mockPdfContent, 'utf8');
  }

  async testLearnMCPExtraction() {
    const startTime = Date.now();
    console.log('📄 Testing LearnMCP PDF extraction...');
    
    try {
      // Import and test PDF extractor
      const { PDFExtractor } = await import('../LearnMCP/modules/content-extractors/pdf-extractor.js');
      const extractor = new PDFExtractor();
      
      // Test extraction
      const extractedContent = await extractor.extract(TEST_CONFIG.testPdfPath);
      
      // Verify extraction results
      if (!extractedContent.content?.curriculum?.modules) {
        throw new Error('PDF extraction did not produce curriculum structure');
      }
      
      if (extractedContent.content.curriculum.modules.length < 3) {
        throw new Error('Insufficient curriculum modules extracted');
      }
      
      console.log(`   ✓ Extracted ${extractedContent.content.curriculum.modules.length} modules`);
      console.log(`   ✓ Curriculum difficulty: ${extractedContent.content.curriculum.difficulty}`);
      console.log(`   ✓ Estimated duration: ${extractedContent.content.curriculum.estimatedDuration}`);
      
      this.results.steps.push('✅ LearnMCP PDF extraction');
      this.results.timing.extraction = Date.now() - startTime;
      this.extractedContent = extractedContent;
      console.log('   ✓ PDF extraction successful\n');
      
    } catch (error) {
      throw new Error(`LearnMCP extraction failed: ${error.message}`);
    }
  }

  async testMemoryMCPStorage() {
    const startTime = Date.now();
    console.log('💾 Testing MemoryMCP curriculum storage...');
    
    try {
      // Import required modules
      const { DataPersistence } = await import('./modules/data-persistence.js');
      const { MemorySync } = await import('./modules/memory-sync.js');
      const { CurriculumIntegration } = await import('./modules/curriculum-integration.js');
      
      // Initialize components
      const dataPersistence = new DataPersistence(TEST_CONFIG.dataDir);
      const memorySync = new MemorySync(dataPersistence);
      const curriculumIntegration = new CurriculumIntegration(dataPersistence, memorySync);
      
      // Store curriculum data
      const storeResult = await curriculumIntegration.storeCurriculumData(
        TEST_CONFIG.projectId, 
        this.extractedContent
      );
      
      if (!storeResult.success) {
        throw new Error('Failed to store curriculum data');
      }
      
      // Verify storage
      const retrievedData = await curriculumIntegration.getCurriculumForComplexityAnalysis(TEST_CONFIG.projectId);
      
      if (!retrievedData || !retrievedData.hasCurriculum) {
        throw new Error('Failed to retrieve stored curriculum data');
      }
      
      console.log(`   ✓ Stored ${storeResult.modules} modules`);
      console.log(`   ✓ Retrieved curriculum with ${retrievedData.totalModules} modules`);
      console.log(`   ✓ Complexity factors: ${retrievedData.complexityFactors.factors.length} factors`);
      
      this.results.steps.push('✅ MemoryMCP curriculum storage');
      this.results.timing.storage = Date.now() - startTime;
      this.curriculumData = retrievedData;
      console.log('   ✓ Curriculum storage successful\n');
      
    } catch (error) {
      throw new Error(`MemoryMCP storage failed: ${error.message}`);
    }
  }

  async testForestIntegration() {
    const startTime = Date.now();
    console.log('🌲 Testing Forest curriculum integration...');
    
    try {
      // Import Forest modules
      const { DataPersistence } = await import('./modules/data-persistence.js');
      const { MemorySync } = await import('./modules/memory-sync.js');
      const { HtaTreeBuilder } = await import('./modules/hta-tree-builder.js');
      
      // Initialize components
      const dataPersistence = new DataPersistence(TEST_CONFIG.dataDir);
      const memorySync = new MemorySync(dataPersistence);
      
      // Create project config
      const config = {
        goal: TEST_CONFIG.goal,
        context: TEST_CONFIG.context,
        created: new Date().toISOString()
      };
      
      await dataPersistence.saveProjectData(TEST_CONFIG.projectId, 'config.json', config);
      
      // Test complexity analysis with curriculum
      const htaBuilder = new HtaTreeBuilder(dataPersistence, null, memorySync);
      const complexityAnalysis = htaBuilder.analyzeGoalComplexity(
        config.goal, 
        config.context, 
        this.curriculumData
      );
      
      if (!complexityAnalysis.curriculumEnhanced) {
        throw new Error('Complexity analysis did not incorporate curriculum data');
      }
      
      console.log(`   ✓ Complexity score: ${complexityAnalysis.score} (curriculum enhanced)`);
      console.log(`   ✓ Recommended depth: ${complexityAnalysis.recommended_depth}`);
      console.log(`   ✓ Main branches: ${complexityAnalysis.main_branches}`);
      console.log(`   ✓ Curriculum modules: ${complexityAnalysis.curriculumModules}`);
      
      this.results.steps.push('✅ Forest curriculum integration');
      this.results.timing.integration = Date.now() - startTime;
      this.complexityAnalysis = complexityAnalysis;
      console.log('   ✓ Forest integration successful\n');
      
    } catch (error) {
      throw new Error(`Forest integration failed: ${error.message}`);
    }
  }

  async testHTATreeBuilding() {
    const startTime = Date.now();
    console.log('🌳 Testing HTA tree building with curriculum...');
    
    try {
      // The HTA tree building would normally be triggered by the full workflow
      // For testing, we verify the structure is curriculum-informed
      
      const expectedBranches = this.curriculumData.totalModules;
      const actualBranches = this.complexityAnalysis.main_branches;
      
      // Verify curriculum influence on tree structure
      if (this.complexityAnalysis.curriculumEnhanced !== true) {
        throw new Error('HTA tree was not enhanced with curriculum data');
      }
      
      // Check if curriculum modules influenced branch count
      const branchDifference = Math.abs(actualBranches - expectedBranches);
      if (branchDifference <= 2) { // Allow some variation
        console.log(`   ✓ Branch count aligned with curriculum (${actualBranches} branches for ${expectedBranches} modules)`);
      } else {
        console.log(`   ⚠ Branch count variation: ${actualBranches} branches for ${expectedBranches} modules`);
      }
      
      console.log(`   ✓ Tree depth: ${this.complexityAnalysis.recommended_depth}`);
      console.log(`   ✓ Tasks per leaf: ${this.complexityAnalysis.tasks_per_leaf}`);
      console.log(`   ✓ Estimated total tasks: ${this.complexityAnalysis.estimated_tasks}`);
      
      this.results.steps.push('✅ HTA tree building with curriculum');
      this.results.timing.treeBuilding = Date.now() - startTime;
      console.log('   ✓ HTA tree building successful\n');
      
    } catch (error) {
      throw new Error(`HTA tree building failed: ${error.message}`);
    }
  }

  async verifyEndToEndWorkflow() {
    const startTime = Date.now();
    console.log('🔄 Verifying end-to-end workflow...');
    
    try {
      // Verify all components are connected
      const checks = [
        { name: 'PDF extraction', condition: !!this.extractedContent },
        { name: 'Curriculum storage', condition: !!this.curriculumData },
        { name: 'Complexity analysis', condition: !!this.complexityAnalysis },
        { name: 'Curriculum enhancement', condition: this.complexityAnalysis?.curriculumEnhanced },
        { name: 'Module integration', condition: this.complexityAnalysis?.curriculumModules > 0 }
      ];
      
      for (const check of checks) {
        if (!check.condition) {
          throw new Error(`End-to-end check failed: ${check.name}`);
        }
        console.log(`   ✓ ${check.name}`);
      }
      
      this.results.steps.push('✅ End-to-end workflow verification');
      this.results.timing.verification = Date.now() - startTime;
      console.log('   ✓ End-to-end workflow verified\n');
      
    } catch (error) {
      throw new Error(`End-to-end verification failed: ${error.message}`);
    }
  }

  async cleanup() {
    try {
      // Clean up test files
      await fs.rm(TEST_CONFIG.dataDir, { recursive: true, force: true });
      await fs.rm(TEST_CONFIG.testPdfPath, { force: true });
    } catch (error) {
      console.warn('⚠ Cleanup warning:', error.message);
    }
  }

  printResults() {
    console.log('📊 Test Results Summary:');
    console.log('========================');
    console.log(`Success: ${this.results.success ? '✅' : '❌'}`);
    console.log(`Steps completed: ${this.results.steps.length}`);
    
    if (this.results.errors.length > 0) {
      console.log('\nErrors:');
      this.results.errors.forEach(error => console.log(`  ❌ ${error}`));
    }
    
    console.log('\nTiming:');
    Object.entries(this.results.timing).forEach(([step, time]) => {
      console.log(`  ${step}: ${time}ms`);
    });
    
    console.log('\nCompleted steps:');
    this.results.steps.forEach(step => console.log(`  ${step}`));
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new CurriculumWorkflowTest();
  test.runCompleteWorkflow().catch(console.error);
}

export { CurriculumWorkflowTest, TEST_CONFIG };
